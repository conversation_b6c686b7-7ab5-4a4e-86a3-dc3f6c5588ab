<!DOCTYPE html>
<html lang="el">
<head>
  <meta charset="UTF-8">
  <title>UTH SIS Grades</title>
  <style>
    body { font-family: sans-serif; margin: 0; padding: 16px; width: 320px; }
    .input-group { margin-bottom: 12px; }
    label { display: block; margin-bottom: 4px; }
    input[type="text"], input[type="password"] { width: 100%; padding: 6px; }
    button { width: 100%; padding: 8px; margin-top: 8px; }
    .error { color: #b00; margin-bottom: 8px; }
    .result { background: #f4f4f4; padding: 8px; margin-top: 12px; max-height: 200px; overflow: auto; }
  </style>
</head>
<body>
  <h2>Βαθμολογίες ΠΘ</h2>
  <p style="font-size: 12px; color: #666; margin-bottom: 16px;">
    Αυτό το extension βοηθά στην εξαγωγή βαθμών από το σύστημα SIS του ΠΘ.
  </p>
  
  <form id="login-form">
    <button type="submit">Άνοιγμα σελίδας SIS</button>
  </form>
  
  <div style="margin-top: 16px;">
    <button id="load-stored" style="width: 100%; margin-bottom: 8px;">Φόρτωση αποθηκευμένων βαθμών</button>
    <button id="extract-current" style="width: 100%;">Εξαγωγή από τρέχουσα σελίδα</button>
  </div>
  <div class="error" id="error"></div>
  <div class="result" id="result"></div>
  <script src="popup.js"></script>
</body>
</html> 