<!DOCTYPE html>
<html lang="el">
<head>
    <meta charset="UTF-8">
    <title>UTH SIS Extension Demo</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            padding: 20px; 
            background: linear-gradient(135deg, #4CAF50, #45a049); 
            color: white; 
            border-radius: 8px; 
        }
        .demo-section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            background: #fafafa; 
        }
        .step { 
            margin: 15px 0; 
            padding: 10px; 
            background: white; 
            border-left: 4px solid #4CAF50; 
        }
        .code { 
            background: #f4f4f4; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            margin: 10px 0; 
        }
        .button { 
            background: #4CAF50; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px 5px; 
        }
        .button:hover { background: #45a049; }
        .success { color: #4CAF50; font-weight: bold; }
        .warning { color: #ff9800; font-weight: bold; }
        .error { color: #f44336; font-weight: bold; }
        .grades-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .grade-card { 
            background: white; 
            padding: 15px; 
            border-radius: 8px; 
            border: 1px solid #ddd; 
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); 
        }
        .grade-value { 
            font-size: 2em; 
            font-weight: bold; 
            color: #4CAF50; 
            text-align: center; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 UTH SIS Grades Extension</h1>
            <p>Αυτόματη εξαγωγή βαθμών από το σύστημα SIS του Πανεπιστημίου Θεσσαλίας</p>
        </div>

        <div class="demo-section">
            <h2>📋 Τι κάνει το Extension</h2>
            <ul>
                <li>✅ Εξάγει αυτόματα βαθμούς από τη σελίδα του SIS</li>
                <li>✅ Αποθηκεύει τους βαθμούς τοπικά στον browser</li>
                <li>✅ Εξάγει σε JSON format για χρήση σε άλλες εφαρμογές</li>
                <li>✅ Ελληνικό interface</li>
                <li>✅ Δεν αποθηκεύει κωδικούς πρόσβασης</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔧 Εγκατάσταση</h2>
            <div class="step">
                <strong>Βήμα 1:</strong> Ανοίξτε τον Chrome/Edge
            </div>
            <div class="step">
                <strong>Βήμα 2:</strong> Πηγαίνετε στο <code>chrome://extensions/</code>
            </div>
            <div class="step">
                <strong>Βήμα 3:</strong> Ενεργοποιήστε το "Developer mode" (επάνω δεξιά)
            </div>
            <div class="step">
                <strong>Βήμα 4:</strong> Κάντε κλικ στο "Load unpacked"
            </div>
            <div class="step">
                <strong>Βήμα 5:</strong> Επιλέξτε το φάκελο <code>uth-sis-extension</code>
            </div>
            
            <button class="button" id="btn-open-extensions">Άνοιγμα Extensions Page</button>
        </div>

        <div class="demo-section">
            <h2>📖 Χρήση</h2>
            <div class="step">
                <strong>1.</strong> Ανοίξτε τη σελίδα του SIS: <a href="https://sis-web.uth.gr" target="_blank">https://sis-web.uth.gr</a>
            </div>
            <div class="step">
                <strong>2.</strong> Συνδεθείτε με τα credentials σας
            </div>
            <div class="step">
                <strong>3.</strong> Πλοηγηθείτε στη σελίδα των βαθμών
            </div>
            <div class="step">
                <strong>4.</strong> Κάντε κλικ στο εικονίδιο του extension
            </div>
            <div class="step">
                <strong>5.</strong> Κάντε κλικ στο "Εξαγωγή από τρέχουσα σελίδα"
            </div>
            
            <button class="button" id="btn-open-test">Δοκιμή με Test Σελίδα</button>
        </div>

        <div class="demo-section">
            <h2>📊 Παράδειγμα Αποτελέσματος</h2>
            <p>Το extension θα εξάγει βαθμούς σε αυτή τη μορφή:</p>
            <div class="code">
[
  {
    "course": "Εισαγωγή στην Πληροφορική",
    "grade": 8.5,
    "semester": "1ο Εξάμηνο",
    "extractedAt": "2024-01-15T10:30:00.000Z"
  },
  {
    "course": "Προγραμματισμός Ι",
    "grade": 9.0,
    "semester": "1ο Εξάμηνο",
    "extractedAt": "2024-01-15T10:30:00.000Z"
  }
]
            </div>
            
            <h3>Προβολή βαθμών:</h3>
            <div class="grades-grid">
                <div class="grade-card">
                    <h4>Εισαγωγή στην Πληροφορική</h4>
                    <div class="grade-value">8.5</div>
                    <p><strong>Εξάμηνο:</strong> 1ο Εξάμηνο</p>
                </div>
                <div class="grade-card">
                    <h4>Προγραμματισμός Ι</h4>
                    <div class="grade-value">9.0</div>
                    <p><strong>Εξάμηνο:</strong> 1ο Εξάμηνο</p>
                </div>
                <div class="grade-card">
                    <h4>Μαθηματικά Ι</h4>
                    <div class="grade-value">7.5</div>
                    <p><strong>Εξάμηνο:</strong> 1ο Εξάμηνο</p>
                </div>
                <div class="grade-card">
                    <h4>Δομές Δεδομένων</h4>
                    <div class="grade-value">8.0</div>
                    <p><strong>Εξάμηνο:</strong> 2ο Εξάμηνο</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔗 Ενσωμάτωση με Frontend</h2>
            <p>Μετά την εξαγωγή, μπορείτε να ανεβάσετε το JSON αρχείο στο frontend:</p>
            <div class="step">
                <strong>1.</strong> Εξάγετε τους βαθμούς με το extension
            </div>
            <div class="step">
                <strong>2.</strong> Κατεβάστε το JSON αρχείο
            </div>
            <div class="step">
                <strong>3.</strong> Πηγαίνετε στο frontend της εφαρμογής
            </div>
            <div class="step">
                <strong>4.</strong> Ανεβάστε το JSON αρχείο για προβολή
            </div>
            
            <button class="button" id="btn-open-frontend">Άνοιγμα Frontend</button>
        </div>

        <div class="demo-section">
            <h2>⚠️ Σημαντικές Σημειώσεις</h2>
            <ul>
                <li class="warning">Το extension λειτουργεί μόνο σε σελίδες του SIS (sis-web.uth.gr)</li>
                <li class="success">Δεν αποθηκεύει κωδικούς πρόσβασης</li>
                <li class="success">Όλα τα δεδομένα αποθηκεύονται τοπικά</li>
                <li class="warning">Απαιτείται σύνδεση στο SIS για να λειτουργήσει</li>
                <li class="error">Αν αλλάξει η δομή της σελίδας, ίσως χρειαστεί ενημέρωση</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🧪 Testing</h2>
            <p>Δοκιμάστε το extension με τις παρακάτω σελίδες:</p>
            <button class="button" id="btn-test-extension">Test Extension Logic</button>
            <button class="button" id="btn-test-grades">Test Grades Page</button>
        </div>
    </div>

    <script>
        function openExtensionsPage() {
            window.open('chrome://extensions/', '_blank');
        }
        
        function openTestPage() {
            window.open('test-grades.html', '_blank');
        }
        
        function openFrontend() {
            // Assuming the frontend is running on localhost:5173
            window.open('http://localhost:5173/grades-fetch', '_blank');
        }
        
        function openTestExtension() {
            window.open('test-extension.html', '_blank');
        }
        
        function openTestGrades() {
            window.open('test-grades.html', '_blank');
        }
        
        // Check if extension is available
        window.addEventListener('load', () => {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                console.log('Extension detected:', chrome.runtime.id);
            } else {
                console.log('Extension not detected - needs to be installed');
            }
            document.getElementById('btn-open-extensions').addEventListener('click', openExtensionsPage);
            document.getElementById('btn-open-test').addEventListener('click', openTestPage);
            document.getElementById('btn-open-frontend').addEventListener('click', openFrontend);
            document.getElementById('btn-test-extension').addEventListener('click', openTestExtension);
            document.getElementById('btn-test-grades').addEventListener('click', openTestGrades);
        });
    </script>
</body>
</html> 