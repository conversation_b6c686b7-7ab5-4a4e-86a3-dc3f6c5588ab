# A string used to distinguish different Supabase projects on the same host. Defaults to the
# working directory name when running `supabase init`.
project_id = "unipost"

[api]
enabled = true
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get
# API endpoints. public and storage are always included.
schemas = ["public", "storage", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returned from a table, view, or stored procedure. Limits payload
# size for accidental or malicious requests.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# Port used by db diff command to initialize the shadow database.
shadow_port = 54320
# The database major version to use. This has to be the same as your remote database's. Run
# `SHOW server_version;` on the remote database to check.
major_version = 15

[db.pooler]
enabled = false
# Port to use for the local connection pooler.
port = 54329
# Specifies when a server connection can be reused by other clients.
# Configure one of the supported pooler modes: `transaction`, `session`.
pool_mode = "transaction"
# How many server connections to allow per user/database pair.
default_pool_size = 15
# Maximum number of client connections allowed.
max_client_conn = 100

[realtime]
enabled = true
# Bind realtime via either IPv4 or IPv6. (default: IPv6)
# ip_version = "IPv6"

[studio]
enabled = true
# Port to use for Supabase Studio.
port = 54323
# External URL of the API server that frontend connects to.
api_url = "http://127.0.0.1:54321"

# Email testing server. Emails sent with the local dev setup are not actually sent - rather, they
# are monitored, and you can view the emails that would have been sent from the web interface.
[inbucket]
enabled = true
# Port to use for the email testing server web interface.
port = 54324
# Uncomment to expose additional ports for testing user applications that send emails.
# smtp_port = 2500
# pop3_port = 1100

[storage]
enabled = true
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
enabled = true
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:3000"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = ["https://localhost:3000"]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604800 (1 week).
jwt_expiry = 3600
# If disabled, the refresh token will never expire.
enable_refresh_token_rotation = true
# Allows refresh tokens to be reused after expiry, up to the specified interval in seconds.
# Requires enable_refresh_token_rotation = true.
refresh_token_reuse_interval = 10
# Allow/disallow new user signups to your project.
enable_signup = true

[auth.email]
# Allow/disallow new user signups via email to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email addresses.
# If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = false

# Uncomment to customize email template
# [auth.email.template.invite]
# subject = "You have been invited"
# content_path = "./supabase/templates/invite.html"

[auth.sms]
# Allow/disallow new user signups via SMS to your project.
enable_signup = true
# If enabled, users need to confirm their phone number before signing in.
enable_confirmations = false
# Template for sending a confirmation message to the user's phone number.
template = "Your code is {{ .Code }}"

[auth.external.apple]
enabled = false
client_id = ""
secret = ""
# Overrides the default auth redirectUrl.
redirect_uri = ""
# Overrides the default auth provider URL. Used to support self-hosted gitlab, single-tenant Azure,
# or any other third-party OIDC providers.
url = ""

[auth.external.azure]
enabled = false
client_id = ""
secret = ""
# Overrides the default auth redirectUrl.
redirect_uri = ""
# Overrides the default auth provider URL. Used to support self-hosted gitlab, single-tenant Azure,
# or any other third-party OIDC providers.
url = ""

[auth.external.bitbucket]
enabled = false
client_id = ""
secret = ""

[auth.external.discord]
enabled = false
client_id = ""
secret = ""

[auth.external.facebook]
enabled = false
client_id = ""
secret = ""

[auth.external.github]
enabled = false
client_id = ""
secret = ""

[auth.external.gitlab]
enabled = false
client_id = ""
secret = ""

[auth.external.google]
enabled = false
client_id = ""
secret = ""

[auth.external.keycloak]
enabled = false
client_id = ""
secret = ""
# Overrides the default auth redirectUrl.
redirect_uri = ""
# Overrides the default auth provider URL. Used to support self-hosted gitlab, single-tenant Azure,
# or any other third-party OIDC providers.
url = ""

[auth.external.linkedin]
enabled = false
client_id = ""
secret = ""

[auth.external.notion]
enabled = false
client_id = ""
secret = ""

[auth.external.slack]
enabled = false
client_id = ""
secret = ""

[auth.external.spotify]
enabled = false
client_id = ""
secret = ""

[auth.external.twitch]
enabled = false
client_id = ""
secret = ""

[auth.external.twitter]
enabled = false
client_id = ""
secret = ""

[auth.external.workos]
enabled = false
client_id = ""
secret = ""
# Overrides the default auth redirectUrl.
redirect_uri = ""
# Overrides the default auth provider URL. Used to support self-hosted gitlab, single-tenant Azure,
# or any other third-party OIDC providers.
url = ""

[auth.external.zoom]
enabled = false
client_id = ""
secret = ""

# Use pre-defined map of phone number to OTP for testing.
# [auth.external.phone]
# enabled = true
# # Map of phone numbers to OTP codes that will be used for testing.
# test_otp = { "**********" = "123456" }

# Use pre-defined map of email to OTP for testing.
# [auth.external.email]
# enabled = true
# # Map of email addresses to OTP codes that will be used for testing.
# test_otp = { "<EMAIL>" = "123456" }

# Configure one of the supported SMS providers: `twilio`, `messagebird`, `textlocal`, `vonage`.
# [auth.sms.twilio]
# enabled = false
# account_sid = ""
# message_service_sid = ""
# # DO NOT commit your Twilio auth token to git. Use environment variable substitution instead:
# auth_token = "env(SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN)"

# Configure one of the supported email providers: `resend`, `sendgrid`, `smtp`.
# [auth.email.resend]
# enabled = false
# api_key = "env(SUPABASE_AUTH_EMAIL_RESEND_API_KEY)"

# Configure SMTP for sending emails. The URL should be in the format: `smtp://username:password@host:port`.
# [auth.email.smtp]
# enabled = false
# host = ""
# port = 587
# user = ""
# pass = ""
# admin_email = ""
# sender_name = ""

# Use an external OAuth provider. The full list of providers are: `apple`, `azure`, `bitbucket`,
# `discord`, `facebook`, `github`, `gitlab`, `google`, `keycloak`, `linkedin`, `notion`, `slack`,
# `spotify`, `twitch`, `twitter`, `workos`, `zoom`.
# [auth.external.apple]
# enabled = true
# client_id = "env(SUPABASE_AUTH_EXTERNAL_APPLE_CLIENT_ID)"
# secret = "env(SUPABASE_AUTH_EXTERNAL_APPLE_SECRET)"
# # Overrides the default auth redirectUrl.
# redirect_uri = "env(SUPABASE_AUTH_EXTERNAL_APPLE_REDIRECT_URI)"
# # Overrides the default auth provider URL. Used to support self-hosted gitlab, single-tenant Azure,
# or any other third-party OIDC providers.
# url = "env(SUPABASE_AUTH_EXTERNAL_APPLE_URL)"

[edge_runtime]
enabled = true
# Configure one of the supported request policies: `oneshot`, `per_worker`.
# request_policy = "oneshot"
# Configure one of the supported response policies: `legacy`, `http`.
# response_policy = "legacy"

[analytics]
enabled = false
# Configure the port for the analytics API.
port = 54327
# Configure the port for the analytics web interface.
web_port = 54328
# Configure the port for the analytics collector.
collector_port = 54326

[functions]
# Configure one of the supported request policies: `oneshot`, `per_worker`.
# request_policy = "oneshot"
# Configure one of the supported response policies: `legacy`, `http`.
# response_policy = "legacy" 