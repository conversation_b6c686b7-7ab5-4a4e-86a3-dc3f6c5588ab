# UTH SIS Grades Extension

Επεκτάσεις για τον Chrome/Edge που επιτρέπει την αυτόματη εξαγωγή βαθμών από το σύστημα SIS του Πανεπιστημίου Θεσσαλίας.

## Χαρακτηριστικά

- Αυτόματη εξαγωγή βαθμών από τη σελίδα του SIS
- Αποθήκευση βαθμών τοπικά για μελλοντική χρήση
- Εξαγωγή σε JSON format
- Ελληνικό interface

## Εγκατάσταση

1. <PERSON><PERSON>ντε clone ή κατεβάστε τα αρχεία του extension
2. Ανοίξτε τον Chrome/Edge και πηγαίνετε στο `chrome://extensions/`
3. Ενεργοποιήστε το "Developer mode" (επάνω δεξιά)
4. <PERSON>άντε κλικ στο "Load unpacked" και επιλέξτε το φάκελο `uth-sis-extension`
5. Το extension θα εμφανιστεί στη λίστα των extensions

## Χρήση

### Μέθοδος 1: Αυτόματη εξαγωγή
1. Κάντε κλικ στο εικονίδιο του extension
2. Κάντε κλικ στο "Άνοιγμα σελίδας SIS"
3. Συνδεθείτε στο σύστημα SIS με τα credentials σας
4. Πλοηγηθείτε στη σελίδα των βαθμών
5. Επιστρέψτε στο extension και κάντε κλικ στο "Εξαγωγή από τρέχουσα σελίδα"

### Μέθοδος 2: Χειροκίνητη εξαγωγή
1. Ανοίξτε τη σελίδα του SIS και συνδεθείτε
2. Πλοηγηθείτε στη σελίδα των βαθμών
3. Κάντε κλικ στο εικονίδιο του extension
4. Κάντε κλικ στο "Εξαγωγή από τρέχουσα σελίδα"

### Προβολή αποθηκευμένων βαθμών
- Κάντε κλικ στο "Φόρτωση αποθηκευμένων βαθμών" για να δείτε τους τελευταίους βαθμούς που εξήχθησαν

### Εξαγωγή σε JSON
- Μετά την εξαγωγή, κάντε κλικ στο "Εξαγωγή σε JSON" για να κατεβάσετε το αρχείο

## Αρχεία

- `manifest.json` - Ρυθμίσεις του extension
- `popup.html` - Το interface του extension
- `popup.js` - Η λογική του popup
- `content.js` - Script που τρέχει στις σελίδες του SIS
- `background.js` - Background service worker

## Ασφάλεια

- Το extension δεν αποθηκεύει κωδικούς πρόσβασης
- Όλα τα δεδομένα αποθηκεύονται τοπικά στον browser
- Δεν στέλνει δεδομένα σε εξωτερικούς servers

## Προβλήματα

Αν το extension δεν λειτουργεί σωστά:

1. Βεβαιωθείτε ότι είστε στη σωστή σελίδα του SIS
2. Ελέγξτε ότι είστε συνδεδεμένοι
3. Δοκιμάστε να ανανεώσετε τη σελίδα
4. Ελέγξτε τα developer tools για σφάλματα

## Ανάπτυξη

Για να τροποποιήσετε το extension:

1. Κάντε τις αλλαγές στα αρχεία
2. Πηγαίνετε στο `chrome://extensions/`
3. Κάντε κλικ στο "Reload" στο extension
4. Δοκιμάστε τις αλλαγές

## Σημειώσεις

- Το extension είναι σε πειραματικό στάδιο
- Η ανάλυση των βαθμών βασίζεται στη δομή της σελίδας του SIS
- Αν αλλάξει η δομή της σελίδας, ίσως χρειαστεί ενημέρωση του extension 