// proxy.js
const express = require('express');
const axios = require('axios');
const app = express();

const API_SECRET = "supersecret123"; // Βάλε εδώ το δικό σου secret!

app.use(express.json());

app.post('/fetch-grades', (req, res, next) => {
  // Έλεγχος για το secret token στο header
  const auth = req.headers['x-api-key'];
  if (auth !== API_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  next();
}, async (req, res) => {
  // Εδώ θα βάλεις το fetch logic προς το SIS
  res.json({ message: "Proxy API λειτουργεί! Βάλε εδώ το fetch logic." });
});

app.listen(8080, '0.0.0.0', () => {
    console.log('Proxy API listening on port 8080');
  });